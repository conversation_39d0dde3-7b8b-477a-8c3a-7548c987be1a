package service

import (
	"bytes"
	"encoding/json"
	"fmt"
	"io"
	"log"
	"net/http"
	"time"

	"go-starter/internal/models"
)

type JupiterService interface {
	GetTokenInfoByAddress(tokenAddress string) (*models.JupiterTokenInfo, error)
	CreateOrder(inputMint, outputMint string, amount uint64, taker string) (*models.JupiterOrderResponse, error)
	ExecuteOrder(signedTransaction, requestID string) (*models.JupiterExecuteResponse, error)
}

type JupiterServiceImpl struct {
	baseURL    string
	httpClient *http.Client
}

func NewJupiterService() JupiterService {
	return &JupiterServiceImpl{
		baseURL: "https://quote-api.jup.ag/v6",
		httpClient: &http.Client{
			Timeout: 30 * time.Second,
		},
	}
}

func (j *JupiterServiceImpl) GetTokenInfoByAddress(tokenAddress string) (*models.JupiterTokenInfo, error) {
	url := fmt.Sprintf("https://lite-api.jup.ag/ultra/v1/search?query=%s", tokenAddress)

	resp, err := j.httpClient.Get(url)
	if err != nil {
		return nil, fmt.Errorf("failed to fetch token info: %w", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		return nil, fmt.Errorf("Jupiter API returned status %d", resp.StatusCode)
	}

	var tokenResponse models.JupiterTokenResponse
	if err := json.NewDecoder(resp.Body).Decode(&tokenResponse); err != nil {
		return nil, fmt.Errorf("failed to decode token response: %w", err)
	}

	if len(tokenResponse) == 0 {
		return nil, fmt.Errorf("token not found: %s", tokenAddress)
	}

	// 返回第一个匹配的代币信息
	return &tokenResponse[0], nil
}

func (j *JupiterServiceImpl) CreateOrder(inputMint, outputMint string, amount uint64, taker string) (*models.JupiterOrderResponse, error) {
	url := fmt.Sprintf("https://lite-api.jup.ag/ultra/v1/order?inputMint=%s&outputMint=%s&amount=%d&taker=%s",
		inputMint, outputMint, amount, taker)

	log.Printf("Creating Jupiter order: %s", url)

	resp, err := j.httpClient.Get(url)
	if err != nil {
		return nil, fmt.Errorf("failed to create order: %w", err)
	}
	defer resp.Body.Close()

	// 读取响应体以便调试
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("failed to read response body: %w", err)
	}

	log.Printf("Jupiter API response status: %d, body: %s", resp.StatusCode, string(body))

	if resp.StatusCode != http.StatusOK {
		return nil, fmt.Errorf("Jupiter Ultra API returned status %d: %s", resp.StatusCode, string(body))
	}

	var orderResponse models.JupiterOrderResponse
	if err := json.Unmarshal(body, &orderResponse); err != nil {
		return nil, fmt.Errorf("failed to decode order response: %w", err)
	}

	return &orderResponse, nil
}

func (j *JupiterServiceImpl) ExecuteOrder(signedTransaction, requestID string) (*models.JupiterExecuteResponse, error) {
	url := "https://lite-api.jup.ag/ultra/v1/execute"

	executeRequest := models.JupiterExecuteRequest{
		SignedTransaction: signedTransaction,
		RequestID:         requestID,
	}

	jsonData, err := json.Marshal(executeRequest)
	if err != nil {
		return nil, fmt.Errorf("failed to marshal execute request: %w", err)
	}

	resp, err := j.httpClient.Post(url, "application/json", bytes.NewBuffer(jsonData))
	if err != nil {
		return nil, fmt.Errorf("failed to execute order: %w", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		return nil, fmt.Errorf("Jupiter Ultra API returned status %d", resp.StatusCode)
	}

	var executeResponse models.JupiterExecuteResponse
	if err := json.NewDecoder(resp.Body).Decode(&executeResponse); err != nil {
		return nil, fmt.Errorf("failed to decode execute response: %w", err)
	}

	return &executeResponse, nil
}
