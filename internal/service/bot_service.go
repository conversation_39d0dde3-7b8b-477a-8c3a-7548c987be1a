package service

import (
	"context"
	"fmt"
	"log"
	"time"

	"go-starter/internal/repository/mysql"
)

type BotService interface {
	ProcessUnbindCommand(userID int64) (string, error)
	ProcessWalletCommand(userID int64) (string, error)
}

type BotServiceImpl struct {
	userRepo       mysql.UserRepository
	jupiterService JupiterService
	contextTimeout time.Duration
}

func NewBotService(userRepo mysql.UserRepository, jupiterService JupiterService, timeout time.Duration) *BotServiceImpl {
	return &BotServiceImpl{
		userRepo:       userRepo,
		jupiterService: jupiterService,
		contextTimeout: timeout,
	}
}

func (b *BotServiceImpl) ProcessUnbindCommand(userID int64) (string, error) {
	ctx, cancel := context.WithTimeout(context.Background(), b.contextTimeout*time.Second)
	defer cancel()

	wallet, err := b.userRepo.GetWallet(ctx, userID)
	if err != nil {
		log.Printf("Error getting wallet: %v", err)
		return "❌ 查询钱包信息失败", err
	}

	if wallet == "" {
		return "❌ 您还没有绑定钱包", nil
	}

	err = b.userRepo.SaveWallet(ctx, userID, "")
	if err != nil {
		log.Printf("Error unbinding wallet: %v", err)
		return "❌ 解绑钱包失败，请稍后重试", err
	}

	return "✅ 钱包解绑成功！", nil
}

func (b *BotServiceImpl) ProcessWalletCommand(userID int64) (string, error) {
	ctx, cancel := context.WithTimeout(context.Background(), b.contextTimeout*time.Second)
	defer cancel()

	wallet, err := b.userRepo.GetWallet(ctx, userID)
	if err != nil {
		log.Printf("Error getting wallet: %v", err)
		return "❌ 查询钱包信息失败", err
	}

	if wallet == "" {
		return "❌ 您还没有绑定钱包。使用 /bind <钱包地址> 来绑定钱包。", nil
	}

	return fmt.Sprintf("💼 您的钱包信息：\n\n📍 地址: `%s`\n\n使用 /balance 查看余额", wallet), nil
}
