package service

import (
	"encoding/json"
	"fmt"
	"io"
	"log"
	"net/http"
	"strconv"
	"time"

	"github.com/gagliardetto/solana-go"
	"go-starter/internal/models"
)

type TokenAnalysisService interface {
	AnalyzeToken(tokenAddress, userWallet string) (*models.TokenAnalysis, error)
	GetTokenInfo(tokenAddress string) (*models.TokenDetailInfo, error)
	GetTokenSecurity(tokenAddress string) (*models.SecurityInfo, error)
	GetTokenHolders(tokenAddress string) ([]models.TokenHolderInfo, error)
	GetPoolInfo(tokenAddress string) ([]models.PoolInfo, error)
}

type TokenAnalysisServiceImpl struct {
	jupiterService JupiterService
	httpClient     *http.Client
}

func (t *TokenAnalysisServiceImpl) GetPoolInfo(tokenAddress string) ([]models.PoolInfo, error) {
	//TODO implement me
	panic("implement me")
}

func NewTokenAnalysisService(jupiterService JupiterService) *TokenAnalysisServiceImpl {
	return &TokenAnalysisServiceImpl{
		jupiterService: jupiterService,
		httpClient: &http.Client{
			Timeout: 30 * time.Second,
		},
	}
}

func (t *TokenAnalysisServiceImpl) AnalyzeToken(tokenAddress, userWallet string) (*models.TokenAnalysis, error) {
	// 验证代币地址格式
	analysis := &models.TokenAnalysis{
		UpdatedAt: time.Now(),
	}

	// 获取基础代币信息
	tokenInfo, err := t.GetTokenInfo(tokenAddress)
	if err != nil || tokenInfo == nil {
		log.Printf("Error getting token info: %v", err)
		// 创建基础信息
		tokenInfo = &models.TokenDetailInfo{
			Address:   tokenAddress,
			Symbol:    "Unknown",
			Name:      "Unknown Token",
			UpdatedAt: time.Now(),
		}
	}

	//// 获取用户持仓信息（仅余额和当前价值）
	//if userWallet != "" {
	//	userBalance, err := t.getUserTokenBalance(tokenAddress, userWallet)
	//	if err != nil {
	//		log.Printf("Error getting user balance: %v", err)
	//	} else {
	//		tokenInfo.UserBalance = userBalance
	//		tokenInfo.UserValue = userBalance * tokenInfo.Price
	//	}
	//}
	//
	//analysis.Token = *tokenInfo
	//
	//// 获取安全信息
	//security, err := t.GetTokenSecurity(tokenAddress)
	//if err != nil {
	//	log.Printf("Error getting security info: %v", err)
	//	security = &models.SecurityInfo{
	//		RiskLevel: "UNKNOWN",
	//		CanTrade:  true,
	//	}
	//}
	//analysis.Security = *security
	//
	//// 获取持有者信息
	//holders, err := t.GetTokenHolders(tokenAddress)
	//if err != nil {
	//	log.Printf("Error getting holders: %v", err)
	//	holders = []models.TokenHolderInfo{}
	//}
	//analysis.Holders = holders
	//
	//// 计算Top5和Top10占比
	//if len(holders) > 0 {
	//	var top5Percent, top10Percent float64
	//	for i, holder := range holders {
	//		if i < 5 {
	//			top5Percent += holder.Percent
	//		}
	//		if i < 10 {
	//			top10Percent += holder.Percent
	//		}
	//	}
	//	analysis.Token.Top5Percent = top5Percent
	//	analysis.Token.Top10Percent = top10Percent
	//	analysis.Token.HolderCount = int64(len(holders))
	//}
	//
	//// 获取池子信息
	//pools, err := t.GetPoolInfo(tokenAddress)
	//if err != nil {
	//	log.Printf("Error getting pool info: %v", err)
	//	pools = []models.PoolInfo{}
	//}
	//analysis.Pools = pools
	//
	//// 设置主要池子信息
	//if len(pools) > 0 {
	//	mainPool := pools[0] // 假设第一个是主要池子
	//	analysis.Token.PoolPlatform = mainPool.Platform
	//	analysis.Token.PoolSOLBalance = mainPool.SOLReserve
	//	analysis.Token.PoolTokenBalance = mainPool.TokenReserve
	//	analysis.Token.PoolCreatedAt = mainPool.CreatedAt
	//}

	return analysis, nil
}

func (t *TokenAnalysisServiceImpl) GetTokenInfo(tokenAddress string) (*models.TokenDetailInfo, error) {
	tokenInfo := &models.TokenDetailInfo{
		Address:   tokenAddress,
		UpdatedAt: time.Now(),
	}

	// 首先尝试从Jupiter Ultra API获取信息
	jupiterData, err := t.jupiterService.GetTokenInfoByAddress(tokenAddress)
	if err == nil && jupiterData != nil {
		tokenInfo.Symbol = jupiterData.Symbol
		tokenInfo.Name = jupiterData.Name
		tokenInfo.Price = jupiterData.USDPrice
		tokenInfo.MarketCap = jupiterData.MCap
		tokenInfo.Volume24h = jupiterData.Stats24h.BuyVolume + jupiterData.Stats24h.SellVolume
		tokenInfo.Change24h = jupiterData.Stats24h.PriceChange
		tokenInfo.LogoURI = jupiterData.Icon
		tokenInfo.Decimals = jupiterData.Decimals
		tokenInfo.TotalSupply = jupiterData.TotalSupply
		tokenInfo.CirculatingSupply = jupiterData.CircSupply
		tokenInfo.HolderCount = int64(jupiterData.HolderCount)
		log.Printf("Got token info from Jupiter Ultra API: %s", tokenInfo.Symbol)
		return tokenInfo, nil
	}
	log.Printf("Jupiter Ultra API failed: %v", err)
	return tokenInfo, nil
}

func (t *TokenAnalysisServiceImpl) GetTokenSecurity(tokenAddress string) (*models.SecurityInfo, error) {
	security := &models.SecurityInfo{
		CanTrade:  true,
		RiskLevel: "LOW",
	}

	// 尝试从RugCheck API获取安全信息
	rugCheckData, err := t.getRugCheckData(tokenAddress)
	if err == nil && rugCheckData != nil {
		security.IsRenounced = rugCheckData.IsRenounced
		security.IsBurned = rugCheckData.IsBurned
		security.IsHoneypot = rugCheckData.IsHoneypot
		security.RiskLevel = rugCheckData.RiskLevel
		security.RiskFactors = rugCheckData.RiskFactors
		log.Printf("Got security info from RugCheck for %s", tokenAddress)
		return security, nil
	}

	log.Printf("RugCheck failed, using basic analysis: %v", err)

	// 如果RugCheck失败，进行基础分析
	// 检查是否是知名代币
	if t.isKnownSafeToken(tokenAddress) {
		security.IsRenounced = true
		security.RiskLevel = "LOW"
		security.RiskFactors = []string{}
		return security, nil
	}

	// 基础风险评估
	riskFactors := []string{}

	// 检查是否是新代币（通过pump.fun检查）
	pumpData, err := t.getPumpFunTokenData(tokenAddress)
	if err == nil && pumpData != nil {
		// 在pump.fun上的代币
		security.RiskLevel = "MEDIUM"
		riskFactors = append(riskFactors, "Token launched on Pump.fun")

		// 检查是否已经毕业到Raydium
		poolInfo, err := t.getPumpFunPoolInfo(tokenAddress)
		if err == nil && poolInfo != nil && !poolInfo.IsActive {
			riskFactors = append(riskFactors, "Graduated from Pump.fun to Raydium")
			security.RiskLevel = "LOW"
		}
	} else {
		// 不在pump.fun上，可能是老代币或其他平台
		security.RiskLevel = "MEDIUM"
		riskFactors = append(riskFactors, "Token not found on major platforms")
	}

	security.RiskFactors = riskFactors
	return security, nil
}

func (t *TokenAnalysisServiceImpl) getRugCheckData(tokenAddress string) (*models.SecurityInfo, error) {
	// 这里可以集成RugCheck或其他安全检查API
	// 暂时返回错误，使用基础分析
	return nil, fmt.Errorf("rugcheck API not implemented")
}

func (t *TokenAnalysisServiceImpl) isKnownSafeToken(tokenAddress string) bool {
	// 检查是否是知名的安全代币
	knownSafeTokens := map[string]bool{
		"EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v": true, // USDC
		"Es9vMFrzaCERmJfrF4H2FYD4KCoNkY11McCe8BenwNYB": true, // USDT
		"So11111111111111111111111111111111111111112":  true, // SOL
		"DezXAZ8z7PnrnRJjz3wXBoRgixCa6xjnB7YaB1pPB263": true, // BONK
		"7vfCXTUXx5WJV5JADk17DUJ4ksgau7utNKj4b963voxs": true, // ETH
		"mSoLzYCxHdYgdzU16g5QSh3i5K3z3KZK7ytfqcJm7So":  true, // mSOL
	}

	return knownSafeTokens[tokenAddress]
}

func (t *TokenAnalysisServiceImpl) GetTokenHolders(tokenAddress string) ([]models.TokenHolderInfo, error) {
	// 尝试从Helius API获取持有者信息
	holders, err := t.getHoldersFromHelius(tokenAddress)
	if err != nil {
		log.Printf("Helius API failed: %v", err)

		// 如果Helius失败，返回模拟数据
		holders = []models.TokenHolderInfo{
			{Address: "7xKXtg2CW87d97TXJSDpbD5jBkheTqA83TZRuJosgAsU", Balance: 1000000, Percent: 15.5, Rank: 1},
			{Address: "EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v", Balance: 800000, Percent: 12.3, Rank: 2},
			{Address: "So11111111111111111111111111111111111111112", Balance: 600000, Percent: 9.2, Rank: 3},
			{Address: "DezXAZ8z7PnrnRJjz3wXBoRgixCa6xjnB7YaB1pPB263", Balance: 500000, Percent: 7.7, Rank: 4},
			{Address: "mSoLzYCxHdYgdzU16g5QSh3i5K3z3KZK7ytfqcJm7So", Balance: 400000, Percent: 6.1, Rank: 5},
		}
	}

	return holders, nil
}

func (t *TokenAnalysisServiceImpl) getHoldersFromHelius(tokenAddress string) ([]models.TokenHolderInfo, error) {
	// 这里可以集成Helius API
	// 由于需要API密钥，暂时返回错误让它使用模拟数据
	return nil, fmt.Errorf("helius API not configured")
}

func (t *TokenAnalysisServiceImpl) getPumpFunTokenData(tokenAddress string) (*models.TokenDetailInfo, error) {
	// Pump.fun API endpoint
	url := fmt.Sprintf("https://frontend-api.pump.fun/coins/%s", tokenAddress)

	resp, err := t.httpClient.Get(url)
	if err != nil {
		return nil, err
	}
	defer resp.Body.Close()

	if resp.StatusCode != 200 {
		return nil, fmt.Errorf("pump.fun API returned status %d", resp.StatusCode)
	}

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, err
	}

	var result struct {
		Mint             string  `json:"mint"`
		Name             string  `json:"name"`
		Symbol           string  `json:"symbol"`
		Description      string  `json:"description"`
		ImageUri         string  `json:"image_uri"`
		UsdMarketCap     float64 `json:"usd_market_cap"`
		ReplyCount       int     `json:"reply_count"`
		Complete         bool    `json:"complete"`
		CreatedTimestamp int64   `json:"created_timestamp"`
	}

	err = json.Unmarshal(body, &result)
	if err != nil {
		return nil, err
	}

	data := &models.TokenDetailInfo{
		Symbol:    result.Symbol,
		Name:      result.Name,
		MarketCap: result.UsdMarketCap,
		LogoURI:   result.ImageUri,
	}

	return data, nil
}

func (t *TokenAnalysisServiceImpl) getDexScreenerData(tokenAddress string) (*models.TokenDetailInfo, error) {
	url := fmt.Sprintf("https://api.dexscreener.com/latest/dex/tokens/%s", tokenAddress)

	resp, err := t.httpClient.Get(url)
	if err != nil {
		return nil, err
	}
	defer resp.Body.Close()

	if resp.StatusCode != 200 {
		return nil, fmt.Errorf("dexscreener API returned status %d", resp.StatusCode)
	}

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, err
	}

	var result struct {
		Pairs []struct {
			BaseToken struct {
				Symbol string `json:"symbol"`
				Name   string `json:"name"`
			} `json:"baseToken"`
			PriceUsd  string `json:"priceUsd"`
			MarketCap string `json:"marketCap"`
			Volume    struct {
				H24 string `json:"h24"`
			} `json:"volume"`
			PriceChange struct {
				H24 string `json:"h24"`
			} `json:"priceChange"`
		} `json:"pairs"`
	}

	err = json.Unmarshal(body, &result)
	if err != nil {
		return nil, err
	}

	if len(result.Pairs) == 0 {
		return nil, fmt.Errorf("no pairs found")
	}

	pair := result.Pairs[0]
	data := &models.TokenDetailInfo{
		Symbol: pair.BaseToken.Symbol,
		Name:   pair.BaseToken.Name,
	}

	if price, err := strconv.ParseFloat(pair.PriceUsd, 64); err == nil {
		data.Price = price
	}
	if marketCap, err := strconv.ParseFloat(pair.MarketCap, 64); err == nil {
		data.MarketCap = marketCap
	}
	if volume, err := strconv.ParseFloat(pair.Volume.H24, 64); err == nil {
		data.Volume24h = volume
	}
	if change, err := strconv.ParseFloat(pair.PriceChange.H24, 64); err == nil {
		data.Change24h = change
	}

	return data, nil
}

func (t *TokenAnalysisServiceImpl) getMintInfo(tokenAddress string) (*MintInfo, error) {
	// 实现获取铸币信息的逻辑
	return nil, fmt.Errorf("not implemented")
}

func (t *TokenAnalysisServiceImpl) getPumpFunPoolInfo(tokenAddress string) (*models.PoolInfo, error) {
	// 检查代币是否在Pump.fun上
	url := fmt.Sprintf("https://frontend-api.pump.fun/coins/%s", tokenAddress)

	resp, err := t.httpClient.Get(url)
	if err != nil {
		return nil, err
	}
	defer resp.Body.Close()

	if resp.StatusCode != 200 {
		return nil, fmt.Errorf("token not found on pump.fun")
	}

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, err
	}

	var result struct {
		Complete             bool    `json:"complete"`
		CreatedTimestamp     int64   `json:"created_timestamp"`
		VirtualSolReserves   float64 `json:"virtual_sol_reserves"`
		VirtualTokenReserves float64 `json:"virtual_token_reserves"`
		RealSolReserves      float64 `json:"real_sol_reserves"`
		RealTokenReserves    float64 `json:"real_token_reserves"`
	}

	err = json.Unmarshal(body, &result)
	if err != nil {
		return nil, err
	}

	poolInfo := &models.PoolInfo{
		Platform:     "Pump.fun",
		Address:      "Pump.fun Bonding Curve",
		SOLReserve:   result.RealSolReserves,
		TokenReserve: result.RealTokenReserves,
		IsActive:     !result.Complete,
		CreatedAt:    time.Unix(result.CreatedTimestamp, 0),
	}

	return poolInfo, nil
}

func (t *TokenAnalysisServiceImpl) getRaydiumPoolsInfo(tokenAddress string) ([]models.PoolInfo, error) {
	// 尝试从Raydium API获取池子信息
	url := fmt.Sprintf("https://api.raydium.io/v2/sdk/liquidity/mainnet.json")

	resp, err := t.httpClient.Get(url)
	if err != nil {
		return nil, err
	}
	defer resp.Body.Close()

	if resp.StatusCode != 200 {
		return nil, fmt.Errorf("raydium API returned status %d", resp.StatusCode)
	}

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, err
	}

	var result struct {
		Official []struct {
			ID               string `json:"id"`
			BaseMint         string `json:"baseMint"`
			QuoteMint        string `json:"quoteMint"`
			LpMint           string `json:"lpMint"`
			BaseDecimals     int    `json:"baseDecimals"`
			QuoteDecimals    int    `json:"quoteDecimals"`
			LpDecimals       int    `json:"lpDecimals"`
			Version          int    `json:"version"`
			ProgramId        string `json:"programId"`
			Authority        string `json:"authority"`
			OpenOrders       string `json:"openOrders"`
			TargetOrders     string `json:"targetOrders"`
			BaseVault        string `json:"baseVault"`
			QuoteVault       string `json:"quoteVault"`
			WithdrawQueue    string `json:"withdrawQueue"`
			LpVault          string `json:"lpVault"`
			MarketVersion    int    `json:"marketVersion"`
			MarketProgramId  string `json:"marketProgramId"`
			MarketId         string `json:"marketId"`
			MarketAuthority  string `json:"marketAuthority"`
			MarketBaseVault  string `json:"marketBaseVault"`
			MarketQuoteVault string `json:"marketQuoteVault"`
			MarketBids       string `json:"marketBids"`
			MarketAsks       string `json:"marketAsks"`
			MarketEventQueue string `json:"marketEventQueue"`
		} `json:"official"`
		UnOfficial []interface{} `json:"unOfficial"`
	}

	err = json.Unmarshal(body, &result)
	if err != nil {
		return nil, err
	}

	var pools []models.PoolInfo

	// 查找包含目标代币的池子
	for _, pool := range result.Official {
		if pool.BaseMint == tokenAddress || pool.QuoteMint == tokenAddress {
			poolInfo := models.PoolInfo{
				Platform:  "Raydium",
				Address:   pool.ID,
				IsActive:  true,
				CreatedAt: time.Now(), // Raydium API不提供创建时间
			}
			pools = append(pools, poolInfo)
		}
	}

	if len(pools) == 0 {
		return nil, fmt.Errorf("no raydium pools found for token")
	}

	return pools, nil
}

// MintInfo represents mint account information
type MintInfo struct {
	MintAuthority   *solana.PublicKey
	FreezeAuthority *solana.PublicKey
	Supply          uint64
	Decimals        uint8
	IsInitialized   bool
}
